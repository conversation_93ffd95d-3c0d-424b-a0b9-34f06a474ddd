# RTLCSS

[![Join the chat at https://gitter.im/<PERSON><PERSON>/rtlcss](https://img.shields.io/gitter/room/<PERSON><PERSON>/rtlcss?color=%2340aa8b)](https://gitter.im/<PERSON><PERSON>/rtlcss)

<img src="https://github.com/<PERSON>nes/rtlcss/blob/master/.github/logo.svg" alt="" align="right" width="100" height="100" title="RTLCSS">

[![GitHub version](https://img.shields.io/github/v/tag/<PERSON><PERSON>/rtlcss)](https://github.com/MohammadYounes/rtlcss/releases)
[![npm version](https://img.shields.io/npm/v/rtlcss)](https://www.npmjs.com/package/rtlcss)
[![CI Status](https://img.shields.io/github/actions/workflow/status/<PERSON><PERSON>/rtlcss/ci.yml?branch=master&label=CI)](https://github.com/<PERSON>nes/rtlcss/actions/workflows/ci.yml?query=branch%3Amaster)

[![js-standard-style](https://img.shields.io/badge/code%20style-standard-blue)](https://standardjs.com/)
[![editor](https://img.shields.io/badge/editor-vscode-blue)](https://code.visualstudio.com/)
[![Twitter](https://img.shields.io/badge/follow-%40rtlcss-blue)](https://twitter.com/rtlcss)

RTLCSS is a framework for converting Left-To-Right (LTR) Cascading Style Sheets(CSS) to Right-To-Left (RTL).

## Documentation

Visit <https://rtlcss.com/learn/>

## Playground

Visit <https://rtlcss.com/playground/>

## Bugs and Issues

Have a bug or a feature request? please feel free to [open a new issue](https://github.com/MohammadYounes/rtlcss/issues/new).

## Release Notes

To view changes in recent versions, see the [CHANGELOG](CHANGELOG.md).

## Support

RTLCSS is saving you and your team a tremendous amount of time and effort? [Buy Me a Coffee ☕](https://www.paypal.me/MohammadYounes)
