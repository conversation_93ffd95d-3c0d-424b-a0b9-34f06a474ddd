{"name": "range-parser", "author": "<PERSON><PERSON> <<EMAIL>> (http://tjholowaychuk.com)", "description": "Range header field string parser", "version": "1.2.0", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>> (http://jongleberry.com)"], "license": "MIT", "keywords": ["range", "parser", "http"], "repository": "jshttp/range-parser", "devDependencies": {"eslint": "2.11.1", "eslint-config-standard": "5.3.1", "eslint-plugin-promise": "1.1.0", "eslint-plugin-standard": "1.3.2", "istanbul": "0.4.3", "mocha": "1.21.5"}, "files": ["HISTORY.md", "LICENSE", "index.js"], "engines": {"node": ">= 0.6"}, "scripts": {"lint": "eslint **/*.js", "test": "mocha --reporter spec", "test-cov": "istanbul cover node_modules/mocha/bin/_mocha -- --reporter dot", "test-travis": "istanbul cover node_modules/mocha/bin/_mocha --report lcovonly -- --reporter dot"}}