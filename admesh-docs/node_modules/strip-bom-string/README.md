# strip-bom-string [![NPM version](https://img.shields.io/npm/v/strip-bom-string.svg?style=flat)](https://www.npmjs.com/package/strip-bom-string) [![NPM monthly downloads](https://img.shields.io/npm/dm/strip-bom-string.svg?style=flat)](https://npmjs.org/package/strip-bom-string)  [![NPM total downloads](https://img.shields.io/npm/dt/strip-bom-string.svg?style=flat)](https://npmjs.org/package/strip-bom-string) [![Linux Build Status](https://img.shields.io/travis/jonschlinkert/strip-bom-string.svg?style=flat&label=Travis)](https://travis-ci.org/jonschlinkert/strip-bom-string)

> Strip a byte order mark (BOM) from a string.

## Install

Install with [npm](https://www.npmjs.com/):

```sh
$ npm install --save strip-bom-string
```

## Usage

```js
var strip = require('strip-bom-string');
strip('\ufefffoo');
//=> 'foo'
```

## About

### Related projects

* [cr](https://www.npmjs.com/package/cr): Strip windows carriage returns, or convert carriage returns to newlines. | [homepage](https://github.com/jonschlinkert/cr "Strip windows carriage returns, or convert carriage returns to newlines.")
* [has-bom](https://www.npmjs.com/package/has-bom): Returns true if a buffer or string has a byte order mark (BOM) | [homepage](https://github.com/jonschlinkert/has-bom "Returns true if a buffer or string has a byte order mark (BOM)")
* [read-file](https://www.npmjs.com/package/read-file): Thin wrapper around fs.readFile and fs.readFileSync that also strips byte order marks when `utf8` encoding… [more](https://github.com/jonschlinkert/read-file) | [homepage](https://github.com/jonschlinkert/read-file "Thin wrapper around fs.readFile and fs.readFileSync that also strips byte order marks when `utf8` encoding is chosen. Also optionally replaces windows newlines with unix newlines.")
* [strip-bom-buffer](https://www.npmjs.com/package/strip-bom-buffer): Strip a byte order mark (BOM) from a buffer. | [homepage](https://github.com/jonschlinkert/strip-bom-buffer "Strip a byte order mark (BOM) from a buffer.")

### Contributing

Pull requests and stars are always welcome. For bugs and feature requests, [please create an issue](../../issues/new).

### Building docs

_(This project's readme.md is generated by [verb](https://github.com/verbose/verb-generate-readme), please don't edit the readme directly. Any changes to the readme must be made in the [.verb.md](.verb.md) readme template.)_

To generate the readme, run the following command:

```sh
$ npm install -g verbose/verb#dev verb-generate-readme && verb
```

### Running tests

Running and reviewing unit tests is a great way to get familiarized with a library and its API. You can install dependencies and run tests with the following command:

```sh
$ npm install && npm test
```

### Author

**Jon Schlinkert**

* [github/jonschlinkert](https://github.com/jonschlinkert)
* [twitter/jonschlinkert](https://twitter.com/jonschlinkert)

### License

Copyright © 2017, [Jon Schlinkert](https://github.com/jonschlinkert).
Released under the [MIT License](LICENSE).

***

_This file was generated by [verb-generate-readme](https://github.com/verbose/verb-generate-readme), v0.4.3, on March 29, 2017._