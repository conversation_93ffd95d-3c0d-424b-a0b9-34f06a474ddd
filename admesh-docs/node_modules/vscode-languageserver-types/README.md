# VSCode Language Server Types

[![NPM Version](https://img.shields.io/npm/v/vscode-languageserver-types.svg)](https://npmjs.org/package/vscode-languageserver-types)
[![NPM Downloads](https://img.shields.io/npm/dm/vscode-languageserver-types.svg)](https://npmjs.org/package/vscode-languageserver-types)
[![Build Status](https://travis-ci.org/Microsoft/vscode-languageserver-types-node.svg?branch=master)](https://travis-ci.org/Microsoft/vscode-languageserver-types-node)

Npm module containing the types used by the VSCode language client and [Node.js](https://nodejs.org/) language server

Click [here](https://code.visualstudio.com/docs/extensions/example-language-server) for a detailed document on how 
to implement language servers for [VSCode](https://code.visualstudio.com/).

## History

For the history please see the [main repository](https://github.com/Microsoft/vscode-languageserver-node/blob/master/README.md)

## License
[MIT](https://github.com/Microsoft/vscode-languageserver-node/blob/master/License.txt)
