{"name": "jest-worker", "version": "27.5.1", "repository": {"type": "git", "url": "https://github.com/facebook/jest.git", "directory": "packages/jest-worker"}, "license": "MIT", "main": "./build/index.js", "types": "./build/index.d.ts", "exports": {".": {"types": "./build/index.d.ts", "default": "./build/index.js"}, "./package.json": "./package.json"}, "dependencies": {"@types/node": "*", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "devDependencies": {"@types/merge-stream": "^1.1.2", "@types/supports-color": "^8.1.0", "get-stream": "^6.0.0", "jest-leak-detector": "^27.5.1", "worker-farm": "^1.6.0"}, "engines": {"node": ">= 10.13.0"}, "publishConfig": {"access": "public"}, "gitHead": "67c1aa20c5fec31366d733e901fee2b981cb1850"}