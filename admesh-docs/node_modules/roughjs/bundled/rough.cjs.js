"use strict";var t=function(e,n){return t=Object.setPrototypeOf||{__proto__:[]}instanceof Array&&function(t,e){t.__proto__=e}||function(t,e){for(var n in e)Object.prototype.hasOwnProperty.call(e,n)&&(t[n]=e[n])},t(e,n)};function e(e,n){if("function"!=typeof n&&null!==n)throw new TypeError("Class extends value "+String(n)+" is not a constructor or null");function r(){this.constructor=e}t(e,n),e.prototype=null===n?Object.create(n):(r.prototype=n.prototype,new r)}var n=function(){return n=Object.assign||function(t){for(var e,n=1,r=arguments.length;n<r;n++)for(var a in e=arguments[n])Object.prototype.hasOwnProperty.call(e,a)&&(t[a]=e[a]);return t},n.apply(this,arguments)};function r(t,e,n){if(n||2===arguments.length)for(var r,a=0,s=e.length;a<s;a++)!r&&a in e||(r||(r=Array.prototype.slice.call(e,0,a)),r[a]=e[a]);return t.concat(r||Array.prototype.slice.call(e))}function a(t,e,n){if(t&&t.length){const[r,a]=e,s=Math.PI/180*n,o=Math.cos(s),i=Math.sin(s);for(const e of t){const[t,n]=e;e[0]=(t-r)*o-(n-a)*i+r,e[1]=(t-r)*i+(n-a)*o+a}}}function s(t,e){return t[0]===e[0]&&t[1]===e[1]}function o(t,e,n,r=1){const o=n,i=Math.max(e,.1),h=t[0]&&t[0][0]&&"number"==typeof t[0][0]?[t]:t,u=[0,0];if(o)for(const t of h)a(t,u,o);const p=function(t,e,n){const r=[];for(const e of t){const t=[...e];s(t[0],t[t.length-1])||t.push([t[0][0],t[0][1]]),t.length>2&&r.push(t)}const a=[];e=Math.max(e,.1);const o=[];for(const t of r)for(let e=0;e<t.length-1;e++){const n=t[e],r=t[e+1];if(n[1]!==r[1]){const t=Math.min(n[1],r[1]);o.push({ymin:t,ymax:Math.max(n[1],r[1]),x:t===n[1]?n[0]:r[0],islope:(r[0]-n[0])/(r[1]-n[1])})}}if(o.sort(((t,e)=>t.ymin<e.ymin?-1:t.ymin>e.ymin?1:t.x<e.x?-1:t.x>e.x?1:t.ymax===e.ymax?0:(t.ymax-e.ymax)/Math.abs(t.ymax-e.ymax))),!o.length)return a;let i=[],h=o[0].ymin,u=0;for(;i.length||o.length;){if(o.length){let t=-1;for(let e=0;e<o.length&&!(o[e].ymin>h);e++)t=e;o.splice(0,t+1).forEach((t=>{i.push({s:h,edge:t})}))}if(i=i.filter((t=>!(t.edge.ymax<=h))),i.sort(((t,e)=>t.edge.x===e.edge.x?0:(t.edge.x-e.edge.x)/Math.abs(t.edge.x-e.edge.x))),(1!==n||u%e==0)&&i.length>1)for(let t=0;t<i.length;t+=2){const e=t+1;if(e>=i.length)break;const n=i[t].edge,r=i[e].edge;a.push([[Math.round(n.x),h],[Math.round(r.x),h]])}h+=n,i.forEach((t=>{t.edge.x=t.edge.x+n*t.edge.islope})),u++}return a}(h,i,r);if(o){for(const t of h)a(t,u,-o);!function(t,e,n){const r=[];t.forEach((t=>r.push(...t))),a(r,e,n)}(p,u,-o)}return p}function i(t,e){var n,r=e.hachureAngle+90,a=e.hachureGap;a<0&&(a=4*e.strokeWidth),a=Math.round(Math.max(a,.1));var s=1;return e.roughness>=1&&((null===(n=e.randomizer)||void 0===n?void 0:n.next())||Math.random())>.7&&(s=a),o(t,a,r,s||1)}"function"==typeof SuppressedError&&SuppressedError;var h=function(){function t(t){this.helper=t}return t.prototype.fillPolygons=function(t,e){return this._fillPolygons(t,e)},t.prototype._fillPolygons=function(t,e){var n=i(t,e);return{type:"fillSketch",ops:this.renderLines(n,e)}},t.prototype.renderLines=function(t,e){for(var n=[],r=0,a=t;r<a.length;r++){var s=a[r];n.push.apply(n,this.helper.doubleLineOps(s[0][0],s[0][1],s[1][0],s[1][1],e))}return n},t}();function u(t){var e=t[0],n=t[1];return Math.sqrt(Math.pow(e[0]-n[0],2)+Math.pow(e[1]-n[1],2))}var p=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.fillPolygons=function(t,e){var n=e.hachureGap;n<0&&(n=4*e.strokeWidth),n=Math.max(n,.1);for(var a=i(t,Object.assign({},e,{hachureGap:n})),s=Math.PI/180*e.hachureAngle,o=[],h=.5*n*Math.cos(s),p=.5*n*Math.sin(s),l=0,c=a;l<c.length;l++){var f=c[l],d=f[0],g=f[1];u([d,g])&&o.push([[d[0]-h,d[1]+p],r([],g,!0)],[[d[0]+h,d[1]-p],r([],g,!0)])}return{type:"fillSketch",ops:this.renderLines(o,e)}},n}(h),l=function(t){function n(){return null!==t&&t.apply(this,arguments)||this}return e(n,t),n.prototype.fillPolygons=function(t,e){var n=this._fillPolygons(t,e),r=Object.assign({},e,{hachureAngle:e.hachureAngle+90}),a=this._fillPolygons(t,r);return n.ops=n.ops.concat(a.ops),n},n}(h),c=function(){function t(t){this.helper=t}return t.prototype.fillPolygons=function(t,e){var n=i(t,e=Object.assign({},e,{hachureAngle:0}));return this.dotsOnLines(n,e)},t.prototype.dotsOnLines=function(t,e){var n=[],r=e.hachureGap;r<0&&(r=4*e.strokeWidth),r=Math.max(r,.1);var a=e.fillWeight;a<0&&(a=e.strokeWidth/2);for(var s=r/4,o=0,i=t;o<i.length;o++)for(var h=i[o],p=u(h),l=p/r,c=Math.ceil(l)-1,f=p-c*r,d=(h[0][0]+h[1][0])/2-r/4,g=Math.min(h[0][1],h[1][1]),y=0;y<c;y++){var v=g+f+y*r,M=d-s+2*Math.random()*s,k=v-s+2*Math.random()*s,b=this.helper.ellipse(M,k,a,a,e);n.push.apply(n,b.ops)}return{type:"fillSketch",ops:n}},t}(),f=function(){function t(t){this.helper=t}return t.prototype.fillPolygons=function(t,e){var n=i(t,e);return{type:"fillSketch",ops:this.dashedLine(n,e)}},t.prototype.dashedLine=function(t,e){var n=this,r=e.dashOffset<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashOffset,a=e.dashGap<0?e.hachureGap<0?4*e.strokeWidth:e.hachureGap:e.dashGap,s=[];return t.forEach((function(t){var o=u(t),i=Math.floor(o/(r+a)),h=(o+a-i*(r+a))/2,p=t[0],l=t[1];p[0]>l[0]&&(p=t[1],l=t[0]);for(var c=Math.atan((l[1]-p[1])/(l[0]-p[0])),f=0;f<i;f++){var d=f*(r+a),g=d+r,y=[p[0]+d*Math.cos(c)+h*Math.cos(c),p[1]+d*Math.sin(c)+h*Math.sin(c)],v=[p[0]+g*Math.cos(c)+h*Math.cos(c),p[1]+g*Math.sin(c)+h*Math.sin(c)];s.push.apply(s,n.helper.doubleLineOps(y[0],y[1],v[0],v[1],e))}})),s},t}(),d=function(){function t(t){this.helper=t}return t.prototype.fillPolygons=function(t,e){var n=e.hachureGap<0?4*e.strokeWidth:e.hachureGap,r=e.zigzagOffset<0?n:e.zigzagOffset,a=i(t,e=Object.assign({},e,{hachureGap:n+r}));return{type:"fillSketch",ops:this.zigzagLines(a,r,e)}},t.prototype.zigzagLines=function(t,e,n){var a=this,s=[];return t.forEach((function(t){var o=u(t),i=Math.round(o/(2*e)),h=t[0],p=t[1];h[0]>p[0]&&(h=t[1],p=t[0]);for(var l=Math.atan((p[1]-h[1])/(p[0]-h[0])),c=0;c<i;c++){var f=2*c*e,d=2*(c+1)*e,g=Math.sqrt(2*Math.pow(e,2)),y=[h[0]+f*Math.cos(l),h[1]+f*Math.sin(l)],v=[h[0]+d*Math.cos(l),h[1]+d*Math.sin(l)],M=[y[0]+g*Math.cos(l+Math.PI/4),y[1]+g*Math.sin(l+Math.PI/4)];s.push.apply(s,r(r([],a.helper.doubleLineOps(y[0],y[1],M[0],M[1],n),!1),a.helper.doubleLineOps(M[0],M[1],v[0],v[1],n),!1))}})),s},t}(),g={};var y=function(){function t(t){this.seed=t}return t.prototype.next=function(){return this.seed?(Math.pow(2,31)-1&(this.seed=Math.imul(48271,this.seed)))/Math.pow(2,31):Math.random()},t}();const v=0,M=1,k=2,b={A:7,a:7,C:6,c:6,H:1,h:1,L:2,l:2,M:2,m:2,Q:4,q:4,S:4,s:4,T:2,t:2,V:1,v:1,Z:0,z:0};function m(t,e){return t.type===e}function w(t){const e=[],n=function(t){const e=new Array;for(;""!==t;)if(t.match(/^([ \t\r\n,]+)/))t=t.substr(RegExp.$1.length);else if(t.match(/^([aAcChHlLmMqQsStTvVzZ])/))e[e.length]={type:v,text:RegExp.$1},t=t.substr(RegExp.$1.length);else{if(!t.match(/^(([-+]?[0-9]+(\.[0-9]*)?|[-+]?\.[0-9]+)([eE][-+]?[0-9]+)?)/))return[];e[e.length]={type:M,text:`${parseFloat(RegExp.$1)}`},t=t.substr(RegExp.$1.length)}return e[e.length]={type:k,text:""},e}(t);let r="BOD",a=0,s=n[a];for(;!m(s,k);){let o=0;const i=[];if("BOD"===r){if("M"!==s.text&&"m"!==s.text)return w("M0,0"+t);a++,o=b[s.text],r=s.text}else m(s,M)?o=b[r]:(a++,o=b[s.text],r=s.text);if(!(a+o<n.length))throw new Error("Path data ended short");for(let t=a;t<a+o;t++){const e=n[t];if(!m(e,M))throw new Error("Param not a number: "+r+","+e.text);i[i.length]=+e.text}if("number"!=typeof b[r])throw new Error("Bad segment: "+r);{const t={key:r,data:i};e.push(t),a+=o,s=n[a],"M"===r&&(r="L"),"m"===r&&(r="l")}}return e}function P(t){let e=0,n=0,r=0,a=0;const s=[];for(const{key:o,data:i}of t)switch(o){case"M":s.push({key:"M",data:[...i]}),[e,n]=i,[r,a]=i;break;case"m":e+=i[0],n+=i[1],s.push({key:"M",data:[e,n]}),r=e,a=n;break;case"L":s.push({key:"L",data:[...i]}),[e,n]=i;break;case"l":e+=i[0],n+=i[1],s.push({key:"L",data:[e,n]});break;case"C":s.push({key:"C",data:[...i]}),e=i[4],n=i[5];break;case"c":{const t=i.map(((t,r)=>r%2?t+n:t+e));s.push({key:"C",data:t}),e=t[4],n=t[5];break}case"Q":s.push({key:"Q",data:[...i]}),e=i[2],n=i[3];break;case"q":{const t=i.map(((t,r)=>r%2?t+n:t+e));s.push({key:"Q",data:t}),e=t[2],n=t[3];break}case"A":s.push({key:"A",data:[...i]}),e=i[5],n=i[6];break;case"a":e+=i[5],n+=i[6],s.push({key:"A",data:[i[0],i[1],i[2],i[3],i[4],e,n]});break;case"H":s.push({key:"H",data:[...i]}),e=i[0];break;case"h":e+=i[0],s.push({key:"H",data:[e]});break;case"V":s.push({key:"V",data:[...i]}),n=i[0];break;case"v":n+=i[0],s.push({key:"V",data:[n]});break;case"S":s.push({key:"S",data:[...i]}),e=i[2],n=i[3];break;case"s":{const t=i.map(((t,r)=>r%2?t+n:t+e));s.push({key:"S",data:t}),e=t[2],n=t[3];break}case"T":s.push({key:"T",data:[...i]}),e=i[0],n=i[1];break;case"t":e+=i[0],n+=i[1],s.push({key:"T",data:[e,n]});break;case"Z":case"z":s.push({key:"Z",data:[]}),e=r,n=a}return s}function x(t){const e=[];let n="",r=0,a=0,s=0,o=0,i=0,h=0;for(const{key:u,data:p}of t){switch(u){case"M":e.push({key:"M",data:[...p]}),[r,a]=p,[s,o]=p;break;case"C":e.push({key:"C",data:[...p]}),r=p[4],a=p[5],i=p[2],h=p[3];break;case"L":e.push({key:"L",data:[...p]}),[r,a]=p;break;case"H":r=p[0],e.push({key:"L",data:[r,a]});break;case"V":a=p[0],e.push({key:"L",data:[r,a]});break;case"S":{let t=0,s=0;"C"===n||"S"===n?(t=r+(r-i),s=a+(a-h)):(t=r,s=a),e.push({key:"C",data:[t,s,...p]}),i=p[0],h=p[1],r=p[2],a=p[3];break}case"T":{const[t,s]=p;let o=0,u=0;"Q"===n||"T"===n?(o=r+(r-i),u=a+(a-h)):(o=r,u=a);const l=r+2*(o-r)/3,c=a+2*(u-a)/3,f=t+2*(o-t)/3,d=s+2*(u-s)/3;e.push({key:"C",data:[l,c,f,d,t,s]}),i=o,h=u,r=t,a=s;break}case"Q":{const[t,n,s,o]=p,u=r+2*(t-r)/3,l=a+2*(n-a)/3,c=s+2*(t-s)/3,f=o+2*(n-o)/3;e.push({key:"C",data:[u,l,c,f,s,o]}),i=t,h=n,r=s,a=o;break}case"A":{const t=Math.abs(p[0]),n=Math.abs(p[1]),s=p[2],o=p[3],i=p[4],h=p[5],u=p[6];if(0===t||0===n)e.push({key:"C",data:[r,a,h,u,h,u]}),r=h,a=u;else if(r!==h||a!==u){O(r,a,h,u,t,n,s,o,i).forEach((function(t){e.push({key:"C",data:t})})),r=h,a=u}break}case"Z":e.push({key:"Z",data:[]}),r=s,a=o}n=u}return e}function S(t,e,n){return[t*Math.cos(n)-e*Math.sin(n),t*Math.sin(n)+e*Math.cos(n)]}function O(t,e,n,r,a,s,o,i,h,u){const p=(l=o,Math.PI*l/180);var l;let c=[],f=0,d=0,g=0,y=0;if(u)[f,d,g,y]=u;else{[t,e]=S(t,e,-p),[n,r]=S(n,r,-p);const o=(t-n)/2,u=(e-r)/2;let l=o*o/(a*a)+u*u/(s*s);l>1&&(l=Math.sqrt(l),a*=l,s*=l);const c=a*a,v=s*s,M=c*v-c*u*u-v*o*o,k=c*u*u+v*o*o,b=(i===h?-1:1)*Math.sqrt(Math.abs(M/k));g=b*a*u/s+(t+n)/2,y=b*-s*o/a+(e+r)/2,f=Math.asin(parseFloat(((e-y)/s).toFixed(9))),d=Math.asin(parseFloat(((r-y)/s).toFixed(9))),t<g&&(f=Math.PI-f),n<g&&(d=Math.PI-d),f<0&&(f=2*Math.PI+f),d<0&&(d=2*Math.PI+d),h&&f>d&&(f-=2*Math.PI),!h&&d>f&&(d-=2*Math.PI)}let v=d-f;if(Math.abs(v)>120*Math.PI/180){const t=d,e=n,i=r;d=h&&d>f?f+120*Math.PI/180*1:f+120*Math.PI/180*-1,c=O(n=g+a*Math.cos(d),r=y+s*Math.sin(d),e,i,a,s,o,0,h,[d,t,g,y])}v=d-f;const M=Math.cos(f),k=Math.sin(f),b=Math.cos(d),m=Math.sin(d),w=Math.tan(v/4),P=4/3*a*w,x=4/3*s*w,L=[t,e],T=[t+P*k,e-x*M],_=[n+P*m,r-x*b],D=[n,r];if(T[0]=2*L[0]-T[0],T[1]=2*L[1]-T[1],u)return[T,_,D].concat(c);{c=[T,_,D].concat(c);const t=[];for(let e=0;e<c.length;e+=3){const n=S(c[e][0],c[e][1],p),r=S(c[e+1][0],c[e+1][1],p),a=S(c[e+2][0],c[e+2][1],p);t.push([n[0],n[1],r[0],r[1],a[0],a[1]])}return t}}var L={randOffset:function(t,e){return F(t,e)},randOffsetWithRange:function(t,e,n){return q(t,e,n)},ellipse:function(t,e,n,r,a){var s=I(n,r,a);return C(t,e,a,s).opset},doubleLineOps:function(t,e,n,r,a){return V(t,e,n,r,a,!0)}};function T(t,e,n,r,a){return{type:"path",ops:V(t,e,n,r,a)}}function _(t,e,n){var r=(t||[]).length;if(r>2){for(var a=[],s=0;s<r-1;s++)a.push.apply(a,V(t[s][0],t[s][1],t[s+1][0],t[s+1][1],n));return e&&a.push.apply(a,V(t[r-1][0],t[r-1][1],t[0][0],t[0][1],n)),{type:"path",ops:a}}return 2===r?T(t[0][0],t[0][1],t[1][0],t[1][1],n):{type:"path",ops:[]}}function D(t,e,n,r,a){return function(t,e){return _(t,!0,e)}([[t,e],[t+n,e],[t+n,e+r],[t,e+r]],a)}function A(t,e){if(t.length){for(var n="number"==typeof t[0][0]?[t]:t,r=Q(n[0],1*(1+.2*e.roughness),e),a=e.disableMultiStroke?[]:Q(n[0],1.5*(1+.22*e.roughness),R(e)),s=1;s<n.length;s++){var o=n[s];if(o.length){for(var i=Q(o,1*(1+.2*e.roughness),e),h=e.disableMultiStroke?[]:Q(o,1.5*(1+.22*e.roughness),R(e)),u=0,p=i;u<p.length;u++){"move"!==(f=p[u]).op&&r.push(f)}for(var l=0,c=h;l<c.length;l++){var f;"move"!==(f=c[l]).op&&a.push(f)}}}return{type:"path",ops:r.concat(a)}}return{type:"path",ops:[]}}function I(t,e,n){var r=Math.sqrt(2*Math.PI*Math.sqrt((Math.pow(t/2,2)+Math.pow(e/2,2))/2)),a=Math.ceil(Math.max(n.curveStepCount,n.curveStepCount/Math.sqrt(200)*r)),s=2*Math.PI/a,o=Math.abs(t/2),i=Math.abs(e/2),h=1-n.curveFitting;return{increment:s,rx:o+=F(o*h,n),ry:i+=F(i*h,n)}}function C(t,e,n,r){var a=$(r.increment,t,e,r.rx,r.ry,1,r.increment*q(.1,q(.4,1,n),n),n),s=a[0],o=a[1],i=H(s,null,n);if(!n.disableMultiStroke&&0!==n.roughness){var h=H($(r.increment,t,e,r.rx,r.ry,1.5,0,n)[0],null,n);i=i.concat(h)}return{estimatedPoints:o,opset:{type:"path",ops:i}}}function z(t,e,n,a,s,o,i,h,u){var p=t,l=e,c=Math.abs(n/2),f=Math.abs(a/2);c+=F(.01*c,u),f+=F(.01*f,u);for(var d=s,g=o;d<0;)d+=2*Math.PI,g+=2*Math.PI;g-d>2*Math.PI&&(d=0,g=2*Math.PI);var y=2*Math.PI/u.curveStepCount,v=Math.min(y/2,(g-d)/2),M=N(v,p,l,c,f,d,g,1,u);if(!u.disableMultiStroke){var k=N(v,p,l,c,f,d,g,1.5,u);M.push.apply(M,k)}return i&&(h?M.push.apply(M,r(r([],V(p,l,p+c*Math.cos(d),l+f*Math.sin(d),u),!1),V(p,l,p+c*Math.cos(g),l+f*Math.sin(g),u),!1)):M.push({op:"lineTo",data:[p,l]},{op:"lineTo",data:[p+c*Math.cos(d),l+f*Math.sin(d)]})),{type:"path",ops:M}}function W(t,e){for(var n=[],r=[0,0],a=[0,0],s=0,o=x(P(w(t)));s<o.length;s++){var i=o[s],h=i.key,u=i.data;switch(h){case"M":a=[u[0],u[1]],r=[u[0],u[1]];break;case"L":n.push.apply(n,V(a[0],a[1],u[0],u[1],e)),a=[u[0],u[1]];break;case"C":var p=u[0],l=u[1],c=u[2],f=u[3],d=u[4],g=u[5];n.push.apply(n,B(p,l,c,f,d,g,a,e)),a=[d,g];break;case"Z":n.push.apply(n,V(a[0],a[1],r[0],r[1],e)),a=[r[0],r[1]]}}return{type:"path",ops:n}}function E(t,e){for(var n=[],r=0,a=t;r<a.length;r++){var s=a[r];if(s.length){var o=e.maxRandomnessOffset||0,i=s.length;if(i>2){n.push({op:"move",data:[s[0][0]+F(o,e),s[0][1]+F(o,e)]});for(var h=1;h<i;h++)n.push({op:"lineTo",data:[s[h][0]+F(o,e),s[h][1]+F(o,e)]})}}}return{type:"fillPath",ops:n}}function G(t,e){return function(t,e){var n=t.fillStyle||"hachure";if(!g[n])switch(n){case"zigzag":g[n]||(g[n]=new p(e));break;case"cross-hatch":g[n]||(g[n]=new l(e));break;case"dots":g[n]||(g[n]=new c(e));break;case"dashed":g[n]||(g[n]=new f(e));break;case"zigzag-line":g[n]||(g[n]=new d(e));break;default:g[n="hachure"]||(g[n]=new h(e))}return g[n]}(e,L).fillPolygons(t,e)}function R(t){var e=n({},t);return e.randomizer=void 0,t.seed&&(e.seed=t.seed+1),e}function j(t){return t.randomizer||(t.randomizer=new y(t.seed||0)),t.randomizer.next()}function q(t,e,n,r){return void 0===r&&(r=1),n.roughness*r*(j(n)*(e-t)+t)}function F(t,e,n){return void 0===n&&(n=1),q(-t,t,e,n)}function V(t,e,n,r,a,s){void 0===s&&(s=!1);var o=s?a.disableMultiStrokeFill:a.disableMultiStroke,i=Z(t,e,n,r,a,!0,!1);if(o)return i;var h=Z(t,e,n,r,a,!0,!0);return i.concat(h)}function Z(t,e,n,r,a,s,o){var i=Math.pow(t-n,2)+Math.pow(e-r,2),h=Math.sqrt(i),u=1;u=h<200?1:h>500?.4:-.0016668*h+1.233334;var p=a.maxRandomnessOffset||0;p*p*100>i&&(p=h/10);var l=p/2,c=.2+.2*j(a),f=a.bowing*a.maxRandomnessOffset*(r-e)/200,d=a.bowing*a.maxRandomnessOffset*(t-n)/200;f=F(f,a,u),d=F(d,a,u);var g=[],y=function(){return F(l,a,u)},v=function(){return F(p,a,u)},M=a.preserveVertices;return s&&(o?g.push({op:"move",data:[t+(M?0:y()),e+(M?0:y())]}):g.push({op:"move",data:[t+(M?0:F(p,a,u)),e+(M?0:F(p,a,u))]})),o?g.push({op:"bcurveTo",data:[f+t+(n-t)*c+y(),d+e+(r-e)*c+y(),f+t+2*(n-t)*c+y(),d+e+2*(r-e)*c+y(),n+(M?0:y()),r+(M?0:y())]}):g.push({op:"bcurveTo",data:[f+t+(n-t)*c+v(),d+e+(r-e)*c+v(),f+t+2*(n-t)*c+v(),d+e+2*(r-e)*c+v(),n+(M?0:v()),r+(M?0:v())]}),g}function Q(t,e,n){if(!t.length)return[];var r=[];r.push([t[0][0]+F(e,n),t[0][1]+F(e,n)]),r.push([t[0][0]+F(e,n),t[0][1]+F(e,n)]);for(var a=1;a<t.length;a++)r.push([t[a][0]+F(e,n),t[a][1]+F(e,n)]),a===t.length-1&&r.push([t[a][0]+F(e,n),t[a][1]+F(e,n)]);return H(r,null,n)}function H(t,e,n){var r=t.length,a=[];if(r>3){var s=[],o=1-n.curveTightness;a.push({op:"move",data:[t[1][0],t[1][1]]});for(var i=1;i+2<r;i++){var h=t[i];s[0]=[h[0],h[1]],s[1]=[h[0]+(o*t[i+1][0]-o*t[i-1][0])/6,h[1]+(o*t[i+1][1]-o*t[i-1][1])/6],s[2]=[t[i+1][0]+(o*t[i][0]-o*t[i+2][0])/6,t[i+1][1]+(o*t[i][1]-o*t[i+2][1])/6],s[3]=[t[i+1][0],t[i+1][1]],a.push({op:"bcurveTo",data:[s[1][0],s[1][1],s[2][0],s[2][1],s[3][0],s[3][1]]})}if(e&&2===e.length){var u=n.maxRandomnessOffset;a.push({op:"lineTo",data:[e[0]+F(u,n),e[1]+F(u,n)]})}}else 3===r?(a.push({op:"move",data:[t[1][0],t[1][1]]}),a.push({op:"bcurveTo",data:[t[1][0],t[1][1],t[2][0],t[2][1],t[2][0],t[2][1]]})):2===r&&a.push.apply(a,Z(t[0][0],t[0][1],t[1][0],t[1][1],n,!0,!0));return a}function $(t,e,n,r,a,s,o,i){var h=[],u=[];if(0===i.roughness){t/=4,u.push([e+r*Math.cos(-t),n+a*Math.sin(-t)]);for(var p=0;p<=2*Math.PI;p+=t){var l=[e+r*Math.cos(p),n+a*Math.sin(p)];h.push(l),u.push(l)}u.push([e+r*Math.cos(0),n+a*Math.sin(0)]),u.push([e+r*Math.cos(t),n+a*Math.sin(t)])}else{var c=F(.5,i)-Math.PI/2;u.push([F(s,i)+e+.9*r*Math.cos(c-t),F(s,i)+n+.9*a*Math.sin(c-t)]);var f=2*Math.PI+c-.01;for(p=c;p<f;p+=t){l=[F(s,i)+e+r*Math.cos(p),F(s,i)+n+a*Math.sin(p)];h.push(l),u.push(l)}u.push([F(s,i)+e+r*Math.cos(c+2*Math.PI+.5*o),F(s,i)+n+a*Math.sin(c+2*Math.PI+.5*o)]),u.push([F(s,i)+e+.98*r*Math.cos(c+o),F(s,i)+n+.98*a*Math.sin(c+o)]),u.push([F(s,i)+e+.9*r*Math.cos(c+.5*o),F(s,i)+n+.9*a*Math.sin(c+.5*o)])}return[u,h]}function N(t,e,n,r,a,s,o,i,h){var u=s+F(.1,h),p=[];p.push([F(i,h)+e+.9*r*Math.cos(u-t),F(i,h)+n+.9*a*Math.sin(u-t)]);for(var l=u;l<=o;l+=t)p.push([F(i,h)+e+r*Math.cos(l),F(i,h)+n+a*Math.sin(l)]);return p.push([e+r*Math.cos(o),n+a*Math.sin(o)]),p.push([e+r*Math.cos(o),n+a*Math.sin(o)]),H(p,null,h)}function B(t,e,n,r,a,s,o,i){for(var h=[],u=[i.maxRandomnessOffset||1,(i.maxRandomnessOffset||1)+.3],p=[0,0],l=i.disableMultiStroke?1:2,c=i.preserveVertices,f=0;f<l;f++)0===f?h.push({op:"move",data:[o[0],o[1]]}):h.push({op:"move",data:[o[0]+(c?0:F(u[0],i)),o[1]+(c?0:F(u[0],i))]}),p=c?[a,s]:[a+F(u[f],i),s+F(u[f],i)],h.push({op:"bcurveTo",data:[t+F(u[f],i),e+F(u[f],i),n+F(u[f],i),r+F(u[f],i),p[0],p[1]]});return h}function J(t){return[...t]}function K(t,e=0){const n=t.length;if(n<3)throw new Error("A curve must have at least three points.");const r=[];if(3===n)r.push(J(t[0]),J(t[1]),J(t[2]),J(t[2]));else{const n=[];n.push(t[0],t[0]);for(let e=1;e<t.length;e++)n.push(t[e]),e===t.length-1&&n.push(t[e]);const a=[],s=1-e;r.push(J(n[0]));for(let t=1;t+2<n.length;t++){const e=n[t];a[0]=[e[0],e[1]],a[1]=[e[0]+(s*n[t+1][0]-s*n[t-1][0])/6,e[1]+(s*n[t+1][1]-s*n[t-1][1])/6],a[2]=[n[t+1][0]+(s*n[t][0]-s*n[t+2][0])/6,n[t+1][1]+(s*n[t][1]-s*n[t+2][1])/6],a[3]=[n[t+1][0],n[t+1][1]],r.push(a[1],a[2],a[3])}}return r}function U(t,e){return Math.pow(t[0]-e[0],2)+Math.pow(t[1]-e[1],2)}function X(t,e,n){const r=U(e,n);if(0===r)return U(t,e);let a=((t[0]-e[0])*(n[0]-e[0])+(t[1]-e[1])*(n[1]-e[1]))/r;return a=Math.max(0,Math.min(1,a)),U(t,Y(e,n,a))}function Y(t,e,n){return[t[0]+(e[0]-t[0])*n,t[1]+(e[1]-t[1])*n]}function tt(t,e,n,r){const a=r||[];if(function(t,e){const n=t[e+0],r=t[e+1],a=t[e+2],s=t[e+3];let o=3*r[0]-2*n[0]-s[0];o*=o;let i=3*r[1]-2*n[1]-s[1];i*=i;let h=3*a[0]-2*s[0]-n[0];h*=h;let u=3*a[1]-2*s[1]-n[1];return u*=u,o<h&&(o=h),i<u&&(i=u),o+i}(t,e)<n){const n=t[e+0];if(a.length){(s=a[a.length-1],o=n,Math.sqrt(U(s,o)))>1&&a.push(n)}else a.push(n);a.push(t[e+3])}else{const r=.5,s=t[e+0],o=t[e+1],i=t[e+2],h=t[e+3],u=Y(s,o,r),p=Y(o,i,r),l=Y(i,h,r),c=Y(u,p,r),f=Y(p,l,r),d=Y(c,f,r);tt([s,u,c,d],0,n,a),tt([d,f,l,h],0,n,a)}var s,o;return a}function et(t,e){return nt(t,0,t.length,e)}function nt(t,e,n,r,a){const s=a||[],o=t[e],i=t[n-1];let h=0,u=1;for(let r=e+1;r<n-1;++r){const e=X(t[r],o,i);e>h&&(h=e,u=r)}return Math.sqrt(h)>r?(nt(t,e,u+1,r,s),nt(t,u,n,r,s)):(s.length||s.push(o),s.push(i)),s}function rt(t,e=.15,n){const r=[],a=(t.length-1)/3;for(let n=0;n<a;n++){tt(t,3*n,e,r)}return n&&n>0?nt(r,0,r.length,n):r}var at="none",st=function(){function t(t){this.defaultOptions={maxRandomnessOffset:2,roughness:1,bowing:1,stroke:"#000",strokeWidth:1,curveTightness:0,curveFitting:.95,curveStepCount:9,fillStyle:"hachure",fillWeight:-1,hachureAngle:-41,hachureGap:-1,dashOffset:-1,dashGap:-1,zigzagOffset:-1,seed:0,disableMultiStroke:!1,disableMultiStrokeFill:!1,preserveVertices:!1,fillShapeRoughnessGain:.8},this.config=t||{},this.config.options&&(this.defaultOptions=this._o(this.config.options))}return t.newSeed=function(){return Math.floor(Math.random()*Math.pow(2,31))},t.prototype._o=function(t){return t?Object.assign({},this.defaultOptions,t):this.defaultOptions},t.prototype._d=function(t,e,n){return{shape:t,sets:e||[],options:n||this.defaultOptions}},t.prototype.line=function(t,e,n,r,a){var s=this._o(a);return this._d("line",[T(t,e,n,r,s)],s)},t.prototype.rectangle=function(t,e,n,r,a){var s=this._o(a),o=[],i=D(t,e,n,r,s);if(s.fill){var h=[[t,e],[t+n,e],[t+n,e+r],[t,e+r]];"solid"===s.fillStyle?o.push(E([h],s)):o.push(G([h],s))}return s.stroke!==at&&o.push(i),this._d("rectangle",o,s)},t.prototype.ellipse=function(t,e,n,r,a){var s=this._o(a),o=[],i=I(n,r,s),h=C(t,e,s,i);if(s.fill)if("solid"===s.fillStyle){var u=C(t,e,s,i).opset;u.type="fillPath",o.push(u)}else o.push(G([h.estimatedPoints],s));return s.stroke!==at&&o.push(h.opset),this._d("ellipse",o,s)},t.prototype.circle=function(t,e,n,r){var a=this.ellipse(t,e,n,n,r);return a.shape="circle",a},t.prototype.linearPath=function(t,e){var n=this._o(e);return this._d("linearPath",[_(t,!1,n)],n)},t.prototype.arc=function(t,e,r,a,s,o,i,h){void 0===i&&(i=!1);var u=this._o(h),p=[],l=z(t,e,r,a,s,o,i,!0,u);if(i&&u.fill)if("solid"===u.fillStyle){var c=n({},u);c.disableMultiStroke=!0;var f=z(t,e,r,a,s,o,!0,!1,c);f.type="fillPath",p.push(f)}else p.push(function(t,e,n,r,a,s,o){var i=t,h=e,u=Math.abs(n/2),p=Math.abs(r/2);u+=F(.01*u,o),p+=F(.01*p,o);for(var l=a,c=s;l<0;)l+=2*Math.PI,c+=2*Math.PI;c-l>2*Math.PI&&(l=0,c=2*Math.PI);for(var f=(c-l)/o.curveStepCount,d=[],g=l;g<=c;g+=f)d.push([i+u*Math.cos(g),h+p*Math.sin(g)]);return d.push([i+u*Math.cos(c),h+p*Math.sin(c)]),d.push([i,h]),G([d],o)}(t,e,r,a,s,o,u));return u.stroke!==at&&p.push(l),this._d("arc",p,u)},t.prototype.curve=function(t,e){var r=this._o(e),a=[],s=A(t,r);if(r.fill&&r.fill!==at)if("solid"===r.fillStyle){var o=A(t,n(n({},r),{disableMultiStroke:!0,roughness:r.roughness?r.roughness+r.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(o.ops)})}else{var i=[],h=t;if(h.length)for(var u=0,p="number"==typeof h[0][0]?[h]:h;u<p.length;u++){var l=p[u];l.length<3?i.push.apply(i,l):3===l.length?i.push.apply(i,rt(K([l[0],l[0],l[1],l[2]]),10,(1+r.roughness)/2)):i.push.apply(i,rt(K(l),10,(1+r.roughness)/2))}i.length&&a.push(G([i],r))}return r.stroke!==at&&a.push(s),this._d("curve",a,r)},t.prototype.polygon=function(t,e){var n=this._o(e),r=[],a=_(t,!0,n);return n.fill&&("solid"===n.fillStyle?r.push(E([t],n)):r.push(G([t],n))),n.stroke!==at&&r.push(a),this._d("polygon",r,n)},t.prototype.path=function(t,e){var r=this._o(e),a=[];if(!t)return this._d("path",a,r);t=(t||"").replace(/\n/g," ").replace(/(-\s)/g,"-").replace("/(ss)/g"," ");var s=r.fill&&"transparent"!==r.fill&&r.fill!==at,o=r.stroke!==at,i=!!(r.simplification&&r.simplification<1),h=function(t,e,n){const r=x(P(w(t))),a=[];let s=[],o=[0,0],i=[];const h=()=>{i.length>=4&&s.push(...rt(i,e)),i=[]},u=()=>{h(),s.length&&(a.push(s),s=[])};for(const{key:t,data:e}of r)switch(t){case"M":u(),o=[e[0],e[1]],s.push(o);break;case"L":h(),s.push([e[0],e[1]]);break;case"C":if(!i.length){const t=s.length?s[s.length-1]:o;i.push([t[0],t[1]])}i.push([e[0],e[1]]),i.push([e[2],e[3]]),i.push([e[4],e[5]]);break;case"Z":h(),s.push([o[0],o[1]])}if(u(),!n)return a;const p=[];for(const t of a){const e=et(t,n);e.length&&p.push(e)}return p}(t,1,i?4-4*(r.simplification||1):(1+r.roughness)/2),u=W(t,r);if(s)if("solid"===r.fillStyle)if(1===h.length){var p=W(t,n(n({},r),{disableMultiStroke:!0,roughness:r.roughness?r.roughness+r.fillShapeRoughnessGain:0}));a.push({type:"fillPath",ops:this._mergedShape(p.ops)})}else a.push(E(h,r));else a.push(G(h,r));return o&&(i?h.forEach((function(t){a.push(_(t,!1,r))})):a.push(u)),this._d("path",a,r)},t.prototype.opsToPath=function(t,e){for(var n="",r=0,a=t.ops;r<a.length;r++){var s=a[r],o="number"==typeof e&&e>=0?s.data.map((function(t){return+t.toFixed(e)})):s.data;switch(s.op){case"move":n+="M".concat(o[0]," ").concat(o[1]," ");break;case"bcurveTo":n+="C".concat(o[0]," ").concat(o[1],", ").concat(o[2]," ").concat(o[3],", ").concat(o[4]," ").concat(o[5]," ");break;case"lineTo":n+="L".concat(o[0]," ").concat(o[1]," ")}}return n.trim()},t.prototype.toPaths=function(t){for(var e=t.sets||[],n=t.options||this.defaultOptions,r=[],a=0,s=e;a<s.length;a++){var o=s[a],i=null;switch(o.type){case"path":i={d:this.opsToPath(o),stroke:n.stroke,strokeWidth:n.strokeWidth,fill:at};break;case"fillPath":i={d:this.opsToPath(o),stroke:at,strokeWidth:0,fill:n.fill||at};break;case"fillSketch":i=this.fillSketch(o,n)}i&&r.push(i)}return r},t.prototype.fillSketch=function(t,e){var n=e.fillWeight;return n<0&&(n=e.strokeWidth/2),{d:this.opsToPath(t),stroke:e.fill||at,strokeWidth:n,fill:at}},t.prototype._mergedShape=function(t){return t.filter((function(t,e){return 0===e||"move"!==t.op}))},t}(),ot=function(){function t(t,e){this.canvas=t,this.ctx=this.canvas.getContext("2d"),this.gen=new st(e)}return t.prototype.draw=function(t){for(var e=t.sets||[],n=t.options||this.getDefaultOptions(),r=this.ctx,a=t.options.fixedDecimalPlaceDigits,s=0,o=e;s<o.length;s++){var i=o[s];switch(i.type){case"path":r.save(),r.strokeStyle="none"===n.stroke?"transparent":n.stroke,r.lineWidth=n.strokeWidth,n.strokeLineDash&&r.setLineDash(n.strokeLineDash),n.strokeLineDashOffset&&(r.lineDashOffset=n.strokeLineDashOffset),this._drawToContext(r,i,a),r.restore();break;case"fillPath":r.save(),r.fillStyle=n.fill||"";var h="curve"===t.shape||"polygon"===t.shape||"path"===t.shape?"evenodd":"nonzero";this._drawToContext(r,i,a,h),r.restore();break;case"fillSketch":this.fillSketch(r,i,n)}}},t.prototype.fillSketch=function(t,e,n){var r=n.fillWeight;r<0&&(r=n.strokeWidth/2),t.save(),n.fillLineDash&&t.setLineDash(n.fillLineDash),n.fillLineDashOffset&&(t.lineDashOffset=n.fillLineDashOffset),t.strokeStyle=n.fill||"",t.lineWidth=r,this._drawToContext(t,e,n.fixedDecimalPlaceDigits),t.restore()},t.prototype._drawToContext=function(t,e,n,r){void 0===r&&(r="nonzero"),t.beginPath();for(var a=0,s=e.ops;a<s.length;a++){var o=s[a],i="number"==typeof n&&n>=0?o.data.map((function(t){return+t.toFixed(n)})):o.data;switch(o.op){case"move":t.moveTo(i[0],i[1]);break;case"bcurveTo":t.bezierCurveTo(i[0],i[1],i[2],i[3],i[4],i[5]);break;case"lineTo":t.lineTo(i[0],i[1])}}"fillPath"===e.type?t.fill(r):t.stroke()},Object.defineProperty(t.prototype,"generator",{get:function(){return this.gen},enumerable:!1,configurable:!0}),t.prototype.getDefaultOptions=function(){return this.gen.defaultOptions},t.prototype.line=function(t,e,n,r,a){var s=this.gen.line(t,e,n,r,a);return this.draw(s),s},t.prototype.rectangle=function(t,e,n,r,a){var s=this.gen.rectangle(t,e,n,r,a);return this.draw(s),s},t.prototype.ellipse=function(t,e,n,r,a){var s=this.gen.ellipse(t,e,n,r,a);return this.draw(s),s},t.prototype.circle=function(t,e,n,r){var a=this.gen.circle(t,e,n,r);return this.draw(a),a},t.prototype.linearPath=function(t,e){var n=this.gen.linearPath(t,e);return this.draw(n),n},t.prototype.polygon=function(t,e){var n=this.gen.polygon(t,e);return this.draw(n),n},t.prototype.arc=function(t,e,n,r,a,s,o,i){void 0===o&&(o=!1);var h=this.gen.arc(t,e,n,r,a,s,o,i);return this.draw(h),h},t.prototype.curve=function(t,e){var n=this.gen.curve(t,e);return this.draw(n),n},t.prototype.path=function(t,e){var n=this.gen.path(t,e);return this.draw(n),n},t}(),it="http://www.w3.org/2000/svg",ht=function(){function t(t,e){this.svg=t,this.gen=new st(e)}return t.prototype.draw=function(t){for(var e=t.sets||[],n=t.options||this.getDefaultOptions(),r=this.svg.ownerDocument||window.document,a=r.createElementNS(it,"g"),s=t.options.fixedDecimalPlaceDigits,o=0,i=e;o<i.length;o++){var h=i[o],u=null;switch(h.type){case"path":(u=r.createElementNS(it,"path")).setAttribute("d",this.opsToPath(h,s)),u.setAttribute("stroke",n.stroke),u.setAttribute("stroke-width",n.strokeWidth+""),u.setAttribute("fill","none"),n.strokeLineDash&&u.setAttribute("stroke-dasharray",n.strokeLineDash.join(" ").trim()),n.strokeLineDashOffset&&u.setAttribute("stroke-dashoffset","".concat(n.strokeLineDashOffset));break;case"fillPath":(u=r.createElementNS(it,"path")).setAttribute("d",this.opsToPath(h,s)),u.setAttribute("stroke","none"),u.setAttribute("stroke-width","0"),u.setAttribute("fill",n.fill||""),"curve"!==t.shape&&"polygon"!==t.shape||u.setAttribute("fill-rule","evenodd");break;case"fillSketch":u=this.fillSketch(r,h,n)}u&&a.appendChild(u)}return a},t.prototype.fillSketch=function(t,e,n){var r=n.fillWeight;r<0&&(r=n.strokeWidth/2);var a=t.createElementNS(it,"path");return a.setAttribute("d",this.opsToPath(e,n.fixedDecimalPlaceDigits)),a.setAttribute("stroke",n.fill||""),a.setAttribute("stroke-width",r+""),a.setAttribute("fill","none"),n.fillLineDash&&a.setAttribute("stroke-dasharray",n.fillLineDash.join(" ").trim()),n.fillLineDashOffset&&a.setAttribute("stroke-dashoffset","".concat(n.fillLineDashOffset)),a},Object.defineProperty(t.prototype,"generator",{get:function(){return this.gen},enumerable:!1,configurable:!0}),t.prototype.getDefaultOptions=function(){return this.gen.defaultOptions},t.prototype.opsToPath=function(t,e){return this.gen.opsToPath(t,e)},t.prototype.line=function(t,e,n,r,a){var s=this.gen.line(t,e,n,r,a);return this.draw(s)},t.prototype.rectangle=function(t,e,n,r,a){var s=this.gen.rectangle(t,e,n,r,a);return this.draw(s)},t.prototype.ellipse=function(t,e,n,r,a){var s=this.gen.ellipse(t,e,n,r,a);return this.draw(s)},t.prototype.circle=function(t,e,n,r){var a=this.gen.circle(t,e,n,r);return this.draw(a)},t.prototype.linearPath=function(t,e){var n=this.gen.linearPath(t,e);return this.draw(n)},t.prototype.polygon=function(t,e){var n=this.gen.polygon(t,e);return this.draw(n)},t.prototype.arc=function(t,e,n,r,a,s,o,i){void 0===o&&(o=!1);var h=this.gen.arc(t,e,n,r,a,s,o,i);return this.draw(h)},t.prototype.curve=function(t,e){var n=this.gen.curve(t,e);return this.draw(n)},t.prototype.path=function(t,e){var n=this.gen.path(t,e);return this.draw(n)},t}(),ut={canvas:function(t,e){return new ot(t,e)},svg:function(t,e){return new ht(t,e)},generator:function(t){return new st(t)},newSeed:function(){return st.newSeed()}};module.exports=ut;
