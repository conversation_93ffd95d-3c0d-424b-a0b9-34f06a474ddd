# 📚 AdMesh Storybook Access Guide

## 🎯 How to Access AdMesh Storybook Ad Formats

AdMesh UI SDK includes a comprehensive **Storybook** showcase that demonstrates the revolutionary storybook-style advertising format. Here's how developers can access and explore it:

## 🚀 Quick Access

### **Option 1: Local Development**

```bash
# Clone the AdMesh UI SDK repository
git clone https://github.com/GouniManikumar12/admesh-ui-sdk.git
cd admesh-ui-sdk

# Install dependencies
npm install

# Start Storybook
npm run storybook
```

**Result**: Storybook opens at `http://localhost:6006`

### **Option 2: Online Demo** (Coming Soon)

Visit the hosted Storybook at: `https://admesh-ui-sdk.netlify.app` *(deployment in progress)*

## 📖 What You'll See in Storybook

### **Main Navigation Structure**

```
📚 AdMesh UI SDK Storybook
├── 📚 AdMesh/Storybook Ad Formats
│   ├── 📖 Startup Journey Story
│   ├── 💻 Developer Workflow Story
│   ├── 📊 Ad Format Comparison
│   └── 🎭 Interactive Demo
├── 📝 Citation/AdMeshCitationUnit
│   ├── 🔢 Numbered Citations
│   ├── 📋 Bracketed Citations
│   ├── ⬆️ Superscript Citations
│   ├── 🌙 Dark Theme
│   └── 📚 Storybook Business Narrative
├── 💬 Conversational/Showcase
│   └── 🎭 Interactive Demo
└── 🎨 AdMesh/Showcase
    ├── ✨ Premium Showcase
    ├── 🌙 Dark Theme Showcase
    └── 📊 Comparison View
```

## 🎭 Key Storybook Features

### **1. Interactive Storybook Ad Formats**

**Location**: `AdMesh/Storybook Ad Formats`

**What You'll See**:
- Complete business narratives with embedded citations
- Real-time click tracking demonstrations
- Side-by-side comparisons with traditional ads
- Interactive theme switching

**Example Story**:
```
📖 The Startup Founder's Journey

Sarah was a brilliant engineer who decided to start her own SaaS 
company. As her customer base grew, she realized she needed better 
tools to manage customer relationships¹ and track her sales pipeline².

References:
¹ HubSpot CRM - Perfect for growing businesses with free tier
² Pipedrive - Visual sales pipeline management
```

### **2. Citation Component Variations**

**Location**: `Citation/AdMeshCitationUnit`

**What You'll See**:
- Different citation styles (numbered, bracketed, superscript)
- Theme variations (light/dark mode)
- Interactive click handlers
- Hover effects and tooltips

### **3. Format Comparison Demos**

**What You'll See**:
- Traditional intrusive ads vs AdMesh citations
- Performance metrics visualization
- User experience comparisons
- Engagement rate demonstrations

## 🔧 Interactive Features

### **Click Tracking Demo**

Every citation in Storybook is clickable and shows:
```javascript
📚 Citation Clicked!

Product: HubSpot CRM
Reason: Perfect for growing businesses...
Match Score: 0.94
Story: The Startup Founder's Journey
Category: Business Growth

Tracked Link: https://useadmesh.com/track?ad_id=hubspot-crm&story=startup-journey
```

### **Theme Switching**

Toggle between:
- **Light Mode**: Clean, professional appearance
- **Dark Mode**: Modern, developer-friendly interface
- **Custom Accents**: Brand color customization

### **Citation Style Options**

- **Numbered**: ¹ ² ³ (default, clean circles)
- **Bracketed**: [1] [2] [3] (academic style)
- **Superscript**: ¹ ² ³ (minimal footprint)

## 📊 Performance Demonstrations

### **Engagement Comparison**

Storybook shows real metrics:

| Traditional Ads | AdMesh Citations |
|----------------|------------------|
| 0.05% CTR | 8-12% Engagement |
| Intrusive | Enhancing |
| User Frustration | User Delight |
| -95% ROI | +300% ROI |

### **Visual Examples**

**Traditional Push Ad**:
```
[🚨 URGENT! CRM SOFTWARE SALE! 🚨]
[⚡ 50% OFF TODAY ONLY! ⚡]
[👆 CLICK NOW OR MISS OUT! 👆]
```

**AdMesh Citation**:
```
For customer management, consider HubSpot¹ for its 
excellent free tier and user-friendly interface.

¹ HubSpot CRM - Perfect for growing businesses
```

## 🎨 Customization Examples

### **Theme Configuration**

```tsx
const theme = {
  mode: 'dark',
  accentColor: '#8b5cf6'
};

<AdMeshCitationUnit theme={theme} />
```

### **Citation Styling**

```tsx
// Academic style
<AdMeshCitationUnit citationStyle="bracketed" />

// Minimal style
<AdMeshCitationUnit citationStyle="superscript" />

// Default style
<AdMeshCitationUnit citationStyle="numbered" />
```

## 📱 Responsive Design Demo

Storybook shows how citations adapt to different screen sizes:

- **Desktop**: Full layout with sidebar references
- **Tablet**: Optimized spacing and typography
- **Mobile**: Stacked layout with touch-friendly interactions

## 🔍 Code Examples

Each Storybook story includes:

### **Implementation Code**
```tsx
import { AdMeshCitationUnit } from 'admesh-ui-sdk';

<AdMeshCitationUnit
  recommendations={recommendations}
  conversationText={storyContent}
  citationStyle="numbered"
  showCitationList={true}
  onRecommendationClick={(adId, link) => {
    console.log('Citation clicked:', adId);
    window.open(link, '_blank');
  }}
/>
```

### **Story Data**
```javascript
const recommendations = [
  {
    title: "HubSpot CRM",
    reason: "Perfect for growing businesses...",
    intent_match_score: 0.94,
    admesh_link: "https://useadmesh.com/track?ad_id=hubspot-crm",
    // ... more fields
  }
];
```

## 🎯 Use Cases Demonstrated

### **1. Business Narratives**
- Startup growth stories
- Scaling challenges
- Tool discovery journeys

### **2. Educational Content**
- Technical tutorials
- Best practice guides
- Case study analyses

### **3. AI Applications**
- Chatbot responses
- AI assistant recommendations
- Conversational commerce

## 🚀 Getting Started

### **Step 1**: Access Storybook
```bash
cd admesh-ui-sdk && npm run storybook
```

### **Step 2**: Explore Examples
- Navigate to "Storybook Ad Formats"
- Try the interactive demos
- Click on citations to see tracking

### **Step 3**: Customize
- Switch themes and citation styles
- Modify the code examples
- Test responsive behavior

### **Step 4**: Implement
- Copy code examples from Storybook
- Install admesh-ui-sdk in your project
- Integrate citation components

## 📚 Documentation Integration

The Storybook examples are also documented in:

- **[Ad Formats Guide](/getting-started/ad-formats)** - Conceptual overview
- **[Implementation Examples](/examples/storybook-ads)** - Complete code examples
- **[UI SDK Documentation](/ui-sdk/installation)** - Installation and setup

## 🆘 Troubleshooting

### **Storybook Won't Start**
```bash
# Clear cache and reinstall
rm -rf node_modules package-lock.json
npm install
npm run storybook
```

### **Stories Not Loading**
- Check browser console for errors
- Ensure all dependencies are installed
- Verify Node.js version (18+)

### **Interactive Features Not Working**
- Enable JavaScript in browser
- Check for ad blockers interfering
- Try in incognito/private mode

## 🔗 Additional Resources

- **[GitHub Repository](https://github.com/GouniManikumar12/admesh-ui-sdk)** - Source code
- **[NPM Package](https://www.npmjs.com/package/admesh-ui-sdk)** - Installation
- **[Documentation Site](https://docs.useadmesh.com)** - Complete guides
- **[AdMesh Dashboard](https://useadmesh.com)** - Get API keys

---

The Storybook showcase provides the most comprehensive way to understand and explore AdMesh's revolutionary storybook advertising format. It's an interactive playground where developers can see, click, and experiment with the future of contextual advertising! 🚀
